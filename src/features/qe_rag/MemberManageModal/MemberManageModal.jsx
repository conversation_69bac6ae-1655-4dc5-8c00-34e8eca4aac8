import { useState, useEffect, useCallback } from 'react';
import { Mo<PERSON>, Tabs, Button, Table, Tag, Form, Input, message, Pagination } from 'antd';
import {
    UserOutlined,
    TeamOutlined,
    AuditOutlined,
    PlusOutlined,
    CopyOutlined
} from '@ant-design/icons';
import {
    getJoinedList,
    getJoinedGroupinfo,
    getGroups,
    checkList,
    applyYes,
    applyNo
} from 'COMMON/api/qe_rag/workgroup';
import commonModel from 'COMMON/models/commonModel';
import { connectModel } from 'COMMON/middleware';
import ragModel from 'COMMON/models/qe_rag/ragModel';
import baseModel from 'COMMON/models/baseModel';
import ApplyGroupDialog from './components/ApplyGroupDialog';
import CreateGroupDialog from './components/CreateGroupDialog';

import styles from './MemberManageModal.module.less';

const { TabPane } = Tabs;

const splitMembers = (memberStr) => {
    if (!memberStr) {
        return [];
    }
    return memberStr.split(';').filter(Boolean);
};

const formatDate = (dateStr) => {
    if (!dateStr) {
        return '';
    }
    return new Date(dateStr).toLocaleDateString();
};

// 我加入的组表格组件
const MyGroupsTable = ({ ragUsername }) => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [editDialogVisible, setEditDialogVisible] = useState(false);
    const [currentEditData, setCurrentEditData] = useState(null);
    const [isEditMode, setIsEditMode] = useState(false);

    const fetchData = useCallback(
        async (params = {}) => {
            setLoading(true);
            try {
                const res = await getJoinedGroupinfo({
                    page: pagination.current,
                    pageSize: pagination.pageSize,
                    ...params
                });
                const dataArray = res?.items || [];
                setData(dataArray);
                setPagination((prev) => ({ ...prev, total: res?.total || dataArray.length }));
            } finally {
                setLoading(false);
            }
        },
        [pagination.current, pagination.pageSize]
    );

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    // 检查是否有编辑权限
    const canEdit = (record) => {
        if (!ragUsername) {
            return false;
        }

        // 检查当前用户是否在管理员列表中
        const admins = splitMembers(record.admin);
        return admins.includes(ragUsername);
    };

    // 处理编辑操作
    const handleEdit = (record) => {
        if (!canEdit(record)) {
            message.warning('您没有权限编辑该群组');
            return;
        }

        setIsEditMode(true);
        setCurrentEditData({
            ...record,
            managerArray: splitMembers(record.manager),
            memberArray: splitMembers(record.member),
            adminArray: splitMembers(record.admin)
        });
        setEditDialogVisible(true);
    };

    // 处理编辑成功
    const handleEditSuccess = () => {
        setEditDialogVisible(false);
        setCurrentEditData(null);
        fetchData(); // 重新获取数据
        message.success('编辑成功');
    };

    const columns = [
        { title: '工作组名称', dataIndex: 'name', key: 'name' },
        {
            title: '管理员',
            dataIndex: 'admin',
            key: 'admin',
            render: (admin) => (
                <div>
                    {splitMembers(admin).map((member, index) => (
                        <Tag key={member} size="small">
                            {member}
                        </Tag>
                    ))}
                </div>
            )
        },
        {
            title: '成员',
            dataIndex: 'member',
            key: 'member',
            render: (member) => (
                <div>
                    {splitMembers(member).map((m, index) => (
                        <Tag key={m} size="small">
                            {m}
                        </Tag>
                    ))}
                </div>
            )
        },
        { title: '创建者', dataIndex: 'createUser', key: 'createUser' },
        { title: '事业群', dataIndex: 'business', key: 'business' },
        { title: '经理', dataIndex: 'manager', key: 'manager' },
        {
            title: 'Token',
            dataIndex: 'token',
            key: 'token',
            width: 120,
            render: (token) => (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <span style={{ fontSize: '12px', color: '#666' }}>
                        {token ? `${token.slice(0, 8)}...` : ''}
                    </span>
                    {token && (
                        <Button
                            size="small"
                            type="text"
                            icon={<CopyOutlined />}
                            onClick={(e) => {
                                e.stopPropagation();
                                navigator.clipboard
                                    .writeText(token)
                                    .then(() => {
                                        message.success('Token已复制到剪贴板');
                                    })
                                    .catch(() => {
                                        message.error('复制失败');
                                    });
                            }}
                            title="复制Token"
                        />
                    )}
                </div>
            )
        },
        {
            title: '创建时间',
            dataIndex: 'createAt',
            key: 'createAt',
            render: formatDate
        },
        {
            title: '更新时间',
            dataIndex: 'updateAt',
            key: 'updateAt',
            render: formatDate
        },
        {
            title: '操作',
            key: 'action',
            width: 100,
            render: (_, record) => (
                <Button
                    size="small"
                    type="primary"
                    onClick={() => handleEdit(record)}
                    disabled={!canEdit(record)}
                >
                    编辑
                </Button>
            )
        }
    ];

    return (
        <>
            <Table
                columns={columns}
                dataSource={Array.isArray(data) ? data : []}
                loading={loading}
                pagination={false}
                rowKey="id"
                scroll={{ x: 1200, y: 300 }}
            />
            <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Pagination
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total) => `共 ${total} 条`}
                    onChange={(page, pageSize) => {
                        setPagination((prev) => ({ ...prev, current: page, pageSize }));
                    }}
                />
            </div>

            <CreateGroupDialog
                visible={editDialogVisible}
                onCancel={() => setEditDialogVisible(false)}
                formData={currentEditData}
                isEdit={isEditMode}
                onSubmitSuccess={handleEditSuccess}
                ragUsername={ragUsername}
            />
        </>
    );
};

// 全部团队表格组件
const AllGroupsTable = ({ refreshTrigger, setCreateDialogVisible, ragUsername }) => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [filterForm] = Form.useForm();
    const [applyDialogVisible, setApplyDialogVisible] = useState(false);
    const [currentApplyGroup, setCurrentApplyGroup] = useState(null);

    const fetchData = useCallback(
        async (params = {}) => {
            setLoading(true);
            try {
                const res = await getGroups({
                    page: pagination.current,
                    size: pagination.pageSize,
                    ...params
                });
                setData(res.items || []);
                setPagination((prev) => ({ ...prev, total: res.total || res.items?.length || 0 }));
            } finally {
                setLoading(false);
            }
        },
        [pagination.current, pagination.pageSize]
    );

    useEffect(() => {
        fetchData();
    }, [fetchData, refreshTrigger]);

    // 检查当前用户是否在该组中（管理员、经理或成员）
    const isUserInGroup = (record) => {
        if (!ragUsername) {
            return false;
        }

        const admins = splitMembers(record.admin);
        const managers = splitMembers(record.manager);
        const members = splitMembers(record.member);

        return admins.includes(ragUsername) ||
               managers.includes(ragUsername) ||
               members.includes(ragUsername);
    };

    // 检查是否有编辑权限（管理员）
    const canEdit = (record) => {
        if (!ragUsername) {
            return false;
        }

        const admins = splitMembers(record.admin);
        return admins.includes(ragUsername);
    };

    const handleFilter = () => {
        const values = filterForm.getFieldsValue();
        fetchData(values);
    };

    const handleApply = (record) => {
        const admins = splitMembers(record.admin);
        if (admins.length === 0) {
            message.error('该工作组没有管理员，无法申请');
            return;
        }

        setCurrentApplyGroup({
            id: record.id,
            name: record.name,
            admins: admins
        });
        setApplyDialogVisible(true);
    };

    const handleApplySuccess = () => {
        message.success('申请已提交，等待审核');
    };

    // 处理编辑操作
    const handleEdit = (record) => {
        if (!canEdit(record)) {
            message.warning('您没有权限编辑该群组');
            return;
        }

        setIsEditMode(true);
        setCurrentEditData({
            ...record,
            managerArray: splitMembers(record.manager),
            memberArray: splitMembers(record.member),
            adminArray: splitMembers(record.admin)
        });
        setEditDialogVisible(true);
    };

    // 处理编辑成功
    const handleEditSuccess = () => {
        setEditDialogVisible(false);
        setCurrentEditData(null);
        fetchData(); // 重新获取数据
        message.success('编辑成功');
    };

    const columns = [
        { title: '工作组名称', dataIndex: 'name', key: 'name' },
        {
            title: '管理员',
            dataIndex: 'admin',
            key: 'admin',
            render: (admin) => (
                <div>
                    {splitMembers(admin).map((member, index) => (
                        <Tag key={member} size="small">
                            {member}
                        </Tag>
                    ))}
                </div>
            )
        },
        {
            title: '成员',
            dataIndex: 'member',
            key: 'member',
            render: (member) => (
                <div>
                    {splitMembers(member).map((m, index) => (
                        <Tag key={member} size="small">
                            {m}
                        </Tag>
                    ))}
                </div>
            )
        },
        { title: '创建者', dataIndex: 'createUser', key: 'createUser' },
        { title: '事业群', dataIndex: 'business', key: 'business' },
        { title: '经理', dataIndex: 'manager', key: 'manager' },
        {
            title: 'Token',
            dataIndex: 'token',
            key: 'token',
            width: 120,
            render: (token) => (
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <span style={{ fontSize: '12px', color: '#666' }}>
                        {token ? `${token.slice(0, 8)}...` : ''}
                    </span>
                    {token && (
                        <Button
                            size="small"
                            type="text"
                            icon={<CopyOutlined />}
                            onClick={(e) => {
                                e.stopPropagation();
                                navigator.clipboard
                                    .writeText(token)
                                    .then(() => {
                                        message.success('Token已复制到剪贴板');
                                    })
                                    .catch(() => {
                                        message.error('复制失败');
                                    });
                            }}
                            title="复制Token"
                        />
                    )}
                </div>
            )
        },
        {
            title: '创建时间',
            dataIndex: 'createAt',
            key: 'createAt',
            render: formatDate
        },
        {
            title: '更新时间',
            dataIndex: 'updateAt',
            key: 'updateAt',
            render: formatDate
        },
        {
            title: '操作',
            key: 'action',
            width: 100,
            render: (_, record) => {
                const userInGroup = isUserInGroup(record);
                const hasEditPermission = canEdit(record);

                if (userInGroup) {
                    // 用户已在组中，显示编辑按钮（仅管理员可编辑）
                    return (
                        <Button
                            size="small"
                            type="primary"
                            onClick={() => handleEdit(record)}
                            disabled={!hasEditPermission}
                            style={{
                                backgroundColor: hasEditPermission ? '#52c41a' : undefined,
                                borderColor: hasEditPermission ? '#52c41a' : undefined
                            }}
                        >
                            编辑
                        </Button>
                    );
                } else {
                    // 用户不在组中，显示申请按钮
                    return (
                        <Button
                            size="small"
                            type="primary"
                            onClick={() => handleApply(record)}
                            style={{
                                backgroundColor: '#1890ff',
                                borderColor: '#1890ff'
                            }}
                        >
                            申请
                        </Button>
                    );
                }
            }
        }
    ];

    return (
        <>
            <Form form={filterForm} layout="inline" style={{ margin: '16px 0' }}>
                <Form.Item name="name" label="工作组名称">
                    <Input placeholder="输入工作组名称" />
                </Form.Item>
                <Form.Item name="manager" label="经理">
                    <Input placeholder="输入经理名称" />
                </Form.Item>
                <Form.Item name="business" label="事业群名称">
                    <Input placeholder="请输入事业群名称" />
                </Form.Item>
                <Form.Item>
                    <Button type="primary" onClick={handleFilter}>
                        查询
                    </Button>
                </Form.Item>
                <Form.Item>
                    <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => setCreateDialogVisible(true)}
                        style={{ marginRight: 24 }}
                    >
                        新增群组
                    </Button>
                </Form.Item>
            </Form>
            <Table
                columns={columns}
                dataSource={Array.isArray(data) ? data : []}
                loading={loading}
                pagination={false}
                rowKey="id"
                scroll={{ x: 1200, y: 300 }}
            />
            <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Pagination
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total) => `共 ${total} 条`}
                    onChange={(page, pageSize) => {
                        setPagination((prev) => ({ ...prev, current: page, pageSize }));
                    }}
                />
            </div>

            <ApplyGroupDialog
                visible={applyDialogVisible}
                onCancel={() => setApplyDialogVisible(false)}
                groupId={currentApplyGroup?.id}
                groupName={currentApplyGroup?.name}
                admins={currentApplyGroup?.admins || []}
                onSubmitSuccess={handleApplySuccess}
            />

            <CreateGroupDialog
                visible={editDialogVisible}
                onCancel={() => setEditDialogVisible(false)}
                formData={currentEditData}
                isEdit={isEditMode}
                onSubmitSuccess={handleEditSuccess}
                ragUsername={ragUsername}
            />
        </>
    );
};

// 待审批列表组件
const PendingApprovalTable = () => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);

    const fetchData = useCallback(async () => {
        setLoading(true);
        try {
            const res = await checkList({});
            setData(res ?? []);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleApprove = async (record) => {
        await applyYes({
            id: record.id,
            workGroupId: record.workGroupId,
            role: record.role,
            userName: record.username
        });
        message.success('审批成功');
        fetchData();
    };

    const handleReject = async (record) => {
        await applyNo({
            id: record.id,
            workGroupId: record.workGroupId,
            role: record.role,
            userName: record.username
        });
        message.success('拒绝成功');
        fetchData();
    };

    const columns = [
        { title: '组ID', dataIndex: 'workGroupId', key: 'workGroupId' },
        { title: '组名称', dataIndex: 'workGroupName', key: 'workGroupName' },
        {
            title: '申请角色',
            dataIndex: 'role',
            key: 'role',
            render: (role) => (
                <Tag color={role === 1 ? 'blue' : 'green'}>{role === 1 ? '管理员' : '成员'}</Tag>
            )
        },
        { title: '申请原因', dataIndex: 'reason', key: 'reason' },
        { title: '申请人', dataIndex: 'userName', key: 'userName' },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <div>
                    <Button
                        size="small"
                        type="primary"
                        onClick={() => handleApprove(record)}
                        style={{ marginRight: 8 }}
                    >
                        同意
                    </Button>
                    <Button size="small" danger onClick={() => handleReject(record)}>
                        拒绝
                    </Button>
                </div>
            )
        }
    ];

    return (
        <Table
            scroll={{ x: 1200, y: 300 }}
            columns={columns}
            dataSource={Array.isArray(data) ? data : []}
            loading={loading}
            pagination={false}
            rowKey="id"
        />
    );
};

const MemberManageModal = ({ visible, onCancel, ragUsername }) => {
    const [activeTab, setActiveTab] = useState('1');
    const [createDialogVisible, setCreateDialogVisible] = useState(false);
    const [refreshTrigger, setRefreshTrigger] = useState(0);

    const handleCreateSuccess = () => {
        setRefreshTrigger((prev) => prev + 1);
    };

    const modalTitle = (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>成员管理</span>
            <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setCreateDialogVisible(true)}
                style={{ marginRight: 24 }}
            >
                新增群组
            </Button>
        </div>
    );

    return (
        <>
            <Modal
                // title={modalTitle}
                open={visible}
                onCancel={onCancel}
                footer={null}
                width={1400}
                height={600}
                className={styles.memberManageModal}
            >
                <Tabs activeKey={activeTab} onChange={setActiveTab} className={styles.groupTabs}>
                    <TabPane
                        tab={
                            <div className={styles.tabLabel}>
                                <UserOutlined className={styles.tabIcon} />
                                <span className={styles.tabText}>我加入的</span>
                            </div>
                        }
                        key="1"
                    >
                        {activeTab === '1' && <MyGroupsTable ragUsername={ragUsername} />}
                    </TabPane>
                    <TabPane
                        tab={
                            <div className={styles.tabLabel}>
                                <TeamOutlined className={styles.tabIcon} />
                                <span className={styles.tabText}>全部团队</span>
                            </div>
                        }
                        key="2"
                    >
                        {activeTab === '2' && <AllGroupsTable refreshTrigger={refreshTrigger} setCreateDialogVisible={setCreateDialogVisible} ragUsername={ragUsername} />}
                    </TabPane>
                    <TabPane
                        tab={
                            <div className={styles.tabLabel}>
                                <AuditOutlined className={styles.tabIcon} />
                                <span className={styles.tabText}>待审批列表</span>
                            </div>
                        }
                        key="3"
                    >
                        {activeTab === '3' && <PendingApprovalTable />}
                    </TabPane>
                </Tabs>
            </Modal>

            <CreateGroupDialog
                visible={createDialogVisible}
                onCancel={() => setCreateDialogVisible(false)}
                onSubmitSuccess={handleCreateSuccess}
            />
        </>
    );
};

export default connectModel([baseModel, commonModel, ragModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    workGroupList: state.common.rag.workGroupList,
    ragUsername: state.common.base.ragUsername,
    joinedWorkGroupList: state.common.rag.joinedWorkGroupList,
    allWorkGroupList: state.common.rag.allWorkGroupList,
    embeddingModelList: state.common.rag.embeddingModelList
}))(MemberManageModal);
