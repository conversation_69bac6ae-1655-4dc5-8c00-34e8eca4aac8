# 成员管理功能测试说明

## 功能描述
在"全部团队"标签页中，根据当前用户是否已经在工作组中，显示不同的操作按钮：

### 按钮逻辑
1. **用户不在组中**：显示"申请"按钮（蓝色 #1890ff）
2. **用户在组中但不是管理员**：显示"编辑"按钮（灰色，禁用状态）
3. **用户在组中且是管理员**：显示"编辑"按钮（绿色 #52c41a，可点击）

### 判断逻辑
- `isUserInGroup(record)`: 检查当前用户是否在该组的管理员、经理或成员列表中
- `canEdit(record)`: 检查当前用户是否在该组的管理员列表中

### 测试场景
1. 创建一个工作组，当前用户为管理员
2. 创建另一个工作组，当前用户为普通成员
3. 创建第三个工作组，当前用户不在其中
4. 在"全部团队"页面查看不同工作组的操作按钮颜色和状态

### 预期结果
- 场景1：绿色"编辑"按钮，可点击
- 场景2：灰色"编辑"按钮，禁用状态
- 场景3：蓝色"申请"按钮，可点击

## 代码修改点
1. 在 `AllGroupsTable` 组件中添加了 `ragUsername` 参数
2. 添加了 `isUserInGroup` 和 `canEdit` 函数来判断用户权限
3. 修改了操作列的渲染逻辑，根据用户状态显示不同按钮
4. 添加了编辑相关的状态管理和处理函数
5. 添加了编辑对话框组件
