import React, { useState } from 'react';
import { Card, Space, Typography, Divider } from 'antd';
import MemberSelect from './MemberSelect';

const { Title, Text } = Typography;

const MemberSelectExample = () => {
    const [multipleValue, setMultipleValue] = useState([]);
    const [singleValue, setSingleValue] = useState(null);
    const [tagsValue, setTagsValue] = useState([]);

    return (
        <div style={{ padding: '20px', maxWidth: '800px' }}>
            <Title level={2}>MemberSelect 组件示例</Title>
            
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                {/* 多选模式 */}
                <Card title="多选模式（默认）" size="small">
                    <Text type="secondary">multiple={true} 或不设置 multiple 属性</Text>
                    <div style={{ marginTop: 10 }}>
                        <MemberSelect
                            multiple={true}
                            placeholder="请选择多个用户"
                            value={multipleValue}
                            onChange={setMultipleValue}
                            style={{ width: '100%' }}
                        />
                    </div>
                    <div style={{ marginTop: 10 }}>
                        <Text>选中值: {JSON.stringify(multipleValue)}</Text>
                    </div>
                </Card>

                {/* 单选模式 */}
                <Card title="单选模式" size="small">
                    <Text type="secondary">multiple={false}</Text>
                    <div style={{ marginTop: 10 }}>
                        <MemberSelect
                            multiple={false}
                            placeholder="请选择一个用户"
                            value={singleValue}
                            onChange={setSingleValue}
                            style={{ width: '100%' }}
                        />
                    </div>
                    <div style={{ marginTop: 10 }}>
                        <Text>选中值: {JSON.stringify(singleValue)}</Text>
                    </div>
                </Card>

                {/* 使用 mode 属性 */}
                <Card title="Tags 模式" size="small">
                    <Text type="secondary">mode="tags" (优先级高于 multiple 属性)</Text>
                    <div style={{ marginTop: 10 }}>
                        <MemberSelect
                            mode="tags"
                            multiple={false} // 这个会被 mode 覆盖
                            placeholder="请选择用户或输入自定义标签"
                            value={tagsValue}
                            onChange={setTagsValue}
                            style={{ width: '100%' }}
                        />
                    </div>
                    <div style={{ marginTop: 10 }}>
                        <Text>选中值: {JSON.stringify(tagsValue)}</Text>
                    </div>
                </Card>

                <Divider />

                {/* API 兼容性说明 */}
                <Card title="API 兼容性" size="small">
                    <Space direction="vertical">
                        <div>
                            <Text strong>新格式 (rag/api/user/query):</Text>
                            <pre style={{ background: '#f5f5f5', padding: '10px', marginTop: '5px' }}>
{`[
    {
        "departmentName": "政策部委二组",
        "imageUrl": "https://erp.baidu.com/avatar/...",
        "name": "maadan"
    }
]`}
                            </pre>
                        </div>
                        <div>
                            <Text strong>旧格式 (/core/user/apigo/list):</Text>
                            <pre style={{ background: '#f5f5f5', padding: '10px', marginTop: '5px' }}>
{`{
    "userInfoList": [
        {
            "username": "testuser1",
            "name": "Test User 1",
            "department": "Test Department",
            "imageUrl": "https://example.com/avatar1.jpg"
        }
    ]
}`}
                            </pre>
                        </div>
                    </Space>
                </Card>
            </Space>
        </div>
    );
};

export default MemberSelectExample;
