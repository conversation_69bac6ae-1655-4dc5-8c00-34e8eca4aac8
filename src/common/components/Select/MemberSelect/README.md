# MemberSelect 组件

用户选择器组件，支持单选和多选模式，兼容新旧 API 返回格式。

## 功能特性

- 支持单选和多选模式
- 兼容新旧 API 返回格式
- 支持头像显示
- 支持部门信息显示
- 支持本地存储缓存

## API 兼容性

### 新格式 (rag/api/user/query)
```json
[
    {
        "departmentName": "政策部委二组",
        "imageUrl": "https://erp.baidu.com/avatar/getAvatar?appCode=ERP&uuap=maadan&token=yzHnjrsjAn",
        "name": "maadan"
    },
    {
        "imageUrl": "https://uuap.baidu.com/files/images/userDefaultImage",
        "name": "maasdev"
    }
]
```

### 旧格式 (/core/user/apigo/list)
```json
{
    "userInfoList": [
        {
            "username": "testuser1",
            "name": "Test User 1",
            "department": "Test Department",
            "imageUrl": "https://example.com/avatar1.jpg"
        }
    ]
}
```

## 使用方法

### 多选模式（默认）
```jsx
import MemberSelect from 'COMMON/components/Select/MemberSelect';

// 默认多选
<MemberSelect 
    onChange={handleChange}
    placeholder="请选择用户"
/>

// 显式指定多选
<MemberSelect 
    multiple={true}
    onChange={handleChange}
    placeholder="请选择用户"
/>
```

### 单选模式
```jsx
import MemberSelect from 'COMMON/components/Select/MemberSelect';

<MemberSelect 
    multiple={false}
    onChange={handleChange}
    placeholder="请选择用户"
/>
```

### 使用 mode 属性（优先级更高）
```jsx
import MemberSelect from 'COMMON/components/Select/MemberSelect';

// 使用 mode 属性会覆盖 multiple 设置
<MemberSelect 
    mode="tags"  // 支持 Ant Design Select 的所有 mode 值
    onChange={handleChange}
    placeholder="请选择用户"
/>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| multiple | boolean | true | 是否支持多选 |
| mode | string | - | Select 组件的 mode 属性，优先级高于 multiple |
| variant | string | 'borderless' | Select 组件的 variant 属性 |
| isSetLocalStorage | boolean | false | 是否启用本地存储 |
| username | string | - | 当前用户名 |
| onChange | function | - | 选择变化回调 |
| placeholder | string | - | 占位符文本 |

## 注意事项

1. `mode` 属性的优先级高于 `multiple` 属性
2. 当路径包含 `/qe_rag` 时，会使用新的 rag API
3. 组件会自动兼容新旧两种 API 返回格式
4. 支持 Ant Design Select 组件的所有其他属性
